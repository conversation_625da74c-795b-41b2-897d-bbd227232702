/**
 * Comprehensive Voice Processing Test Suite
 * Tests for non-reversibility, low latency, and quality requirements
 */

import { UnifiedVoiceProcessor, UNIFIED_VOICE_PROFILES } from '../services/unifiedVoiceProcessor';
import { RealTimeAudioStreaming } from '../services/realTimeAudioStreaming';
import { VoicePerformanceMonitor } from '../services/voicePerformanceMonitor';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as crypto from 'crypto';

interface TestResult {
    testName: string;
    success: boolean;
    duration: number;
    details: any;
    error?: string;
}

interface TestSuite {
    suiteName: string;
    results: TestResult[];
    summary: {
        total: number;
        passed: number;
        failed: number;
        averageDuration: number;
    };
}

export class VoiceProcessingTestSuite {
    private unifiedProcessor: UnifiedVoiceProcessor;
    private streamingProcessor: RealTimeAudioStreaming;
    private performanceMonitor: VoicePerformanceMonitor;
    private testAudioBuffer: Buffer;

    constructor() {
        this.unifiedProcessor = new UnifiedVoiceProcessor();
        this.streamingProcessor = new RealTimeAudioStreaming();
        this.performanceMonitor = new VoicePerformanceMonitor();
        this.testAudioBuffer = this.generateTestAudio();
    }

    /**
     * Generate test audio buffer (sine wave)
     */
    private generateTestAudio(duration: number = 1.0, frequency: number = 440): Buffer {
        const sampleRate = 44100;
        const samples = Math.floor(sampleRate * duration);
        const buffer = Buffer.alloc(samples * 2); // 16-bit audio

        for (let i = 0; i < samples; i++) {
            const sample = Math.sin(2 * Math.PI * frequency * i / sampleRate) * 16383;
            buffer.writeInt16LE(Math.round(sample), i * 2);
        }

        return buffer;
    }

    /**
     * Run all test suites
     */
    async runAllTests(): Promise<TestSuite[]> {
        console.log('🧪 Starting comprehensive voice processing tests...');

        const testSuites: TestSuite[] = [
            await this.testLatencyRequirements(),
            await this.testInputToneRemoval(),
            await this.testNonReversibility(),
            await this.testQualityPreservation(),
            await this.testRealTimeStreaming(),
            await this.testPerformanceMonitoring(),
            await this.testSystemReliability(),
            await this.testSecurityFeatures()
        ];

        // Generate summary report
        const overallSummary = this.generateOverallSummary(testSuites);
        console.log('📊 Test Summary:', overallSummary);

        return testSuites;
    }

    /**
     * Test latency requirements (<100ms for real-time)
     */
    private async testLatencyRequirements(): Promise<TestSuite> {
        const results: TestResult[] = [];

        for (const [profileName, profile] of Object.entries(UNIFIED_VOICE_PROFILES)) {
            const startTime = Date.now();
            
            try {
                await this.unifiedProcessor.processVoiceUnified(this.testAudioBuffer, profile);
                const duration = Date.now() - startTime;
                const withinTarget = duration <= profile.performance.latencyTarget;

                results.push({
                    testName: `Latency Test - ${profileName}`,
                    success: withinTarget,
                    duration,
                    details: {
                        processingTime: duration,
                        latencyTarget: profile.performance.latencyTarget,
                        withinTarget,
                        qualityMode: profile.performance.qualityMode
                    }
                });
            } catch (error: any) {
                results.push({
                    testName: `Latency Test - ${profileName}`,
                    success: false,
                    duration: Date.now() - startTime,
                    details: {},
                    error: error.message
                });
            }
        }

        return this.createTestSuite('Latency Requirements', results);
    }

    /**
     * Test input tone removal effectiveness
     */
    private async testInputToneRemoval(): Promise<TestSuite> {
        const results: TestResult[] = [];

        // Test with different input characteristics
        const testInputs = [
            { name: 'Male Voice Simulation', frequency: 120, description: 'Low frequency male voice' },
            { name: 'Female Voice Simulation', frequency: 220, description: 'Higher frequency female voice' },
            { name: 'Child Voice Simulation', frequency: 300, description: 'High frequency child voice' }
        ];

        for (const input of testInputs) {
            const inputBuffer = this.generateTestAudio(0.5, input.frequency);
            
            for (const [profileName, profile] of Object.entries(UNIFIED_VOICE_PROFILES)) {
                if (!profile.security.inputToneRemoval) continue;

                const startTime = Date.now();
                
                try {
                    const processedAudio = await this.unifiedProcessor.processVoiceUnified(inputBuffer, profile);
                    const duration = Date.now() - startTime;

                    // Analyze spectral differences (simplified)
                    const spectralDifference = this.analyzeSpectralDifference(inputBuffer, processedAudio);
                    const toneRemovalEffective = spectralDifference > 0.3; // Threshold for significant change

                    results.push({
                        testName: `Input Tone Removal - ${profileName} - ${input.name}`,
                        success: toneRemovalEffective,
                        duration,
                        details: {
                            inputType: input.description,
                            spectralDifference,
                            toneRemovalEffective,
                            inputSize: inputBuffer.length,
                            outputSize: processedAudio.length
                        }
                    });
                } catch (error: any) {
                    results.push({
                        testName: `Input Tone Removal - ${profileName} - ${input.name}`,
                        success: false,
                        duration: Date.now() - startTime,
                        details: { inputType: input.description },
                        error: error.message
                    });
                }
            }
        }

        return this.createTestSuite('Input Tone Removal', results);
    }

    /**
     * Test non-reversibility of processing
     */
    private async testNonReversibility(): Promise<TestSuite> {
        const results: TestResult[] = [];

        for (const [profileName, profile] of Object.entries(UNIFIED_VOICE_PROFILES)) {
            if (profile.security.reversible) continue; // Skip reversible profiles

            const startTime = Date.now();
            
            try {
                const processedAudio = await this.unifiedProcessor.processVoiceUnified(this.testAudioBuffer, profile);
                const duration = Date.now() - startTime;

                // Test that we cannot recover original from processed
                const recoveryAttempt = this.attemptRecovery(this.testAudioBuffer, processedAudio);
                const nonReversible = recoveryAttempt.similarity < 0.5; // Low similarity indicates non-reversibility

                results.push({
                    testName: `Non-Reversibility Test - ${profileName}`,
                    success: nonReversible,
                    duration,
                    details: {
                        similarity: recoveryAttempt.similarity,
                        nonReversible,
                        antiForensic: profile.security.antiForensic,
                        processingSteps: profile.pipeline
                    }
                });
            } catch (error: any) {
                results.push({
                    testName: `Non-Reversibility Test - ${profileName}`,
                    success: false,
                    duration: Date.now() - startTime,
                    details: {},
                    error: error.message
                });
            }
        }

        return this.createTestSuite('Non-Reversibility', results);
    }

    /**
     * Test quality preservation
     */
    private async testQualityPreservation(): Promise<TestSuite> {
        const results: TestResult[] = [];

        for (const [profileName, profile] of Object.entries(UNIFIED_VOICE_PROFILES)) {
            const startTime = Date.now();
            
            try {
                const processedAudio = await this.unifiedProcessor.processVoiceUnified(this.testAudioBuffer, profile);
                const duration = Date.now() - startTime;

                // Analyze quality metrics
                const qualityMetrics = this.analyzeAudioQuality(this.testAudioBuffer, processedAudio);
                const qualityPreserved = qualityMetrics.snr > 10; // Signal-to-noise ratio threshold

                results.push({
                    testName: `Quality Preservation - ${profileName}`,
                    success: qualityPreserved,
                    duration,
                    details: {
                        ...qualityMetrics,
                        qualityPreserved,
                        qualityMode: profile.performance.qualityMode
                    }
                });
            } catch (error: any) {
                results.push({
                    testName: `Quality Preservation - ${profileName}`,
                    success: false,
                    duration: Date.now() - startTime,
                    details: {},
                    error: error.message
                });
            }
        }

        return this.createTestSuite('Quality Preservation', results);
    }

    /**
     * Test real-time streaming capabilities
     */
    private async testRealTimeStreaming(): Promise<TestSuite> {
        const results: TestResult[] = [];

        // Test session creation and management
        const profileNames = Object.keys(UNIFIED_VOICE_PROFILES).slice(0, 3); // Test first 3 profiles

        for (const profileName of profileNames) {
            const startTime = Date.now();
            
            try {
                // Create streaming session
                const sessionId = this.streamingProcessor.createStreamingSession(profileName);
                
                // Test session operations
                const session = this.streamingProcessor.getSession(sessionId);
                const pauseSuccess = this.streamingProcessor.pauseSession(sessionId);
                const resumeSuccess = this.streamingProcessor.resumeSession(sessionId);
                const stopSuccess = this.streamingProcessor.stopSession(sessionId);

                const duration = Date.now() - startTime;

                results.push({
                    testName: `Real-time Streaming - ${profileName}`,
                    success: session !== undefined && pauseSuccess && resumeSuccess && stopSuccess,
                    duration,
                    details: {
                        sessionCreated: session !== undefined,
                        pauseSuccess,
                        resumeSuccess,
                        stopSuccess,
                        sessionId
                    }
                });
            } catch (error: any) {
                results.push({
                    testName: `Real-time Streaming - ${profileName}`,
                    success: false,
                    duration: Date.now() - startTime,
                    details: {},
                    error: error.message
                });
            }
        }

        return this.createTestSuite('Real-time Streaming', results);
    }

    /**
     * Test performance monitoring
     */
    private async testPerformanceMonitoring(): Promise<TestSuite> {
        const results: TestResult[] = [];

        const startTime = Date.now();
        
        try {
            // Reset metrics for clean test
            this.unifiedProcessor.resetPerformanceMetrics();
            
            // Process some audio to generate metrics
            const profile = UNIFIED_VOICE_PROFILES.ULTIMATE_SECURE_FAST;
            await this.unifiedProcessor.processVoiceUnified(this.testAudioBuffer, profile);
            
            // Test performance monitoring functions
            const stats = this.unifiedProcessor.getPerformanceStats();
            const health = this.unifiedProcessor.getSystemHealth();
            const trends = this.unifiedProcessor.getPerformanceTrends(1);
            
            const duration = Date.now() - startTime;

            results.push({
                testName: 'Performance Monitoring',
                success: stats.totalProcessed > 0 && health.status !== undefined,
                duration,
                details: {
                    statsGenerated: stats.totalProcessed > 0,
                    healthStatus: health.status,
                    trendsGenerated: trends.length >= 0,
                    averageLatency: stats.averageLatency
                }
            });
        } catch (error: any) {
            results.push({
                testName: 'Performance Monitoring',
                success: false,
                duration: Date.now() - startTime,
                details: {},
                error: error.message
            });
        }

        return this.createTestSuite('Performance Monitoring', results);
    }

    /**
     * Test system reliability under load
     */
    private async testSystemReliability(): Promise<TestSuite> {
        const results: TestResult[] = [];

        const startTime = Date.now();
        
        try {
            // Test concurrent processing
            const profile = UNIFIED_VOICE_PROFILES.ULTIMATE_SECURE_BALANCED;
            const concurrentPromises = [];
            
            for (let i = 0; i < 5; i++) {
                concurrentPromises.push(
                    this.unifiedProcessor.processVoiceUnified(this.testAudioBuffer, profile)
                );
            }
            
            const results_concurrent = await Promise.allSettled(concurrentPromises);
            const successCount = results_concurrent.filter(r => r.status === 'fulfilled').length;
            
            const duration = Date.now() - startTime;

            results.push({
                testName: 'Concurrent Processing',
                success: successCount === 5,
                duration,
                details: {
                    totalRequests: 5,
                    successfulRequests: successCount,
                    failedRequests: 5 - successCount,
                    successRate: successCount / 5
                }
            });
        } catch (error: any) {
            results.push({
                testName: 'Concurrent Processing',
                success: false,
                duration: Date.now() - startTime,
                details: {},
                error: error.message
            });
        }

        return this.createTestSuite('System Reliability', results);
    }

    /**
     * Test security features
     */
    private async testSecurityFeatures(): Promise<TestSuite> {
        const results: TestResult[] = [];

        // Test anti-forensic profiles
        const antiForensicProfiles = Object.entries(UNIFIED_VOICE_PROFILES)
            .filter(([_, profile]) => profile.security.antiForensic);

        for (const [profileName, profile] of antiForensicProfiles) {
            const startTime = Date.now();
            
            try {
                const processedAudio = await this.unifiedProcessor.processVoiceUnified(this.testAudioBuffer, profile);
                const duration = Date.now() - startTime;

                // Test security features
                const securityAnalysis = this.analyzeSecurityFeatures(this.testAudioBuffer, processedAudio);

                results.push({
                    testName: `Security Features - ${profileName}`,
                    success: securityAnalysis.antiForensicEffective,
                    duration,
                    details: {
                        ...securityAnalysis,
                        inputToneRemoval: profile.security.inputToneRemoval,
                        antiForensic: profile.security.antiForensic,
                        reversible: profile.security.reversible
                    }
                });
            } catch (error: any) {
                results.push({
                    testName: `Security Features - ${profileName}`,
                    success: false,
                    duration: Date.now() - startTime,
                    details: {},
                    error: error.message
                });
            }
        }

        return this.createTestSuite('Security Features', results);
    }

    // Helper methods for analysis

    private analyzeSpectralDifference(original: Buffer, processed: Buffer): number {
        // Simplified spectral analysis - in production would use FFT
        const originalSamples = new Int16Array(original.buffer, original.byteOffset, original.length / 2);
        const processedSamples = new Int16Array(processed.buffer, processed.byteOffset, processed.length / 2);
        
        let difference = 0;
        const minLength = Math.min(originalSamples.length, processedSamples.length);
        
        for (let i = 0; i < minLength; i++) {
            difference += Math.abs(originalSamples[i] - processedSamples[i]);
        }
        
        return difference / (minLength * 32767); // Normalize
    }

    private attemptRecovery(original: Buffer, processed: Buffer): { similarity: number } {
        // Simplified recovery attempt - measure similarity
        const similarity = 1 - this.analyzeSpectralDifference(original, processed);
        return { similarity: Math.max(0, similarity) };
    }

    private analyzeAudioQuality(original: Buffer, processed: Buffer): any {
        // Simplified quality analysis
        const spectralDiff = this.analyzeSpectralDifference(original, processed);
        const snr = Math.max(0, 20 - spectralDiff * 20); // Simplified SNR calculation
        
        return {
            snr,
            spectralDifference: spectralDiff,
            dynamicRange: 16, // Assuming 16-bit audio
            clarity: snr > 15 ? 'good' : snr > 10 ? 'fair' : 'poor'
        };
    }

    private analyzeSecurityFeatures(original: Buffer, processed: Buffer): any {
        const spectralDiff = this.analyzeSpectralDifference(original, processed);
        const antiForensicEffective = spectralDiff > 0.4; // High threshold for anti-forensic
        
        return {
            antiForensicEffective,
            spectralMasking: spectralDiff,
            patternObfuscation: antiForensicEffective,
            traceabilityReduction: spectralDiff > 0.3
        };
    }

    private createTestSuite(suiteName: string, results: TestResult[]): TestSuite {
        const passed = results.filter(r => r.success).length;
        const failed = results.length - passed;
        const averageDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;

        return {
            suiteName,
            results,
            summary: {
                total: results.length,
                passed,
                failed,
                averageDuration
            }
        };
    }

    private generateOverallSummary(testSuites: TestSuite[]): any {
        const totalTests = testSuites.reduce((sum, suite) => sum + suite.summary.total, 0);
        const totalPassed = testSuites.reduce((sum, suite) => sum + suite.summary.passed, 0);
        const totalFailed = testSuites.reduce((sum, suite) => sum + suite.summary.failed, 0);
        const overallDuration = testSuites.reduce((sum, suite) => sum + suite.summary.averageDuration, 0);

        return {
            totalTestSuites: testSuites.length,
            totalTests,
            totalPassed,
            totalFailed,
            successRate: totalPassed / totalTests,
            averageDuration: overallDuration / testSuites.length,
            suiteResults: testSuites.map(suite => ({
                name: suite.suiteName,
                passed: suite.summary.passed,
                failed: suite.summary.failed,
                successRate: suite.summary.passed / suite.summary.total
            }))
        };
    }

    /**
     * Export test results
     */
    async exportTestResults(testSuites: TestSuite[], format: 'json' | 'html' = 'json'): Promise<string> {
        if (format === 'json') {
            return JSON.stringify({
                timestamp: new Date().toISOString(),
                testSuites,
                summary: this.generateOverallSummary(testSuites)
            }, null, 2);
        } else {
            // Generate HTML report
            return this.generateHtmlReport(testSuites);
        }
    }

    private generateHtmlReport(testSuites: TestSuite[]): string {
        const summary = this.generateOverallSummary(testSuites);
        
        return `
<!DOCTYPE html>
<html>
<head>
    <title>Voice Processing Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .summary { background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .suite { margin-bottom: 30px; border: 1px solid #ddd; border-radius: 5px; }
        .suite-header { background: #e9ecef; padding: 10px; font-weight: bold; }
        .test-result { padding: 10px; border-bottom: 1px solid #eee; }
        .success { color: green; }
        .failure { color: red; }
        .details { font-size: 0.9em; color: #666; margin-top: 5px; }
    </style>
</head>
<body>
    <h1>Voice Processing Test Report</h1>
    <div class="summary">
        <h2>Summary</h2>
        <p>Total Tests: ${summary.totalTests}</p>
        <p>Passed: <span class="success">${summary.totalPassed}</span></p>
        <p>Failed: <span class="failure">${summary.totalFailed}</span></p>
        <p>Success Rate: ${(summary.successRate * 100).toFixed(1)}%</p>
        <p>Generated: ${new Date().toISOString()}</p>
    </div>
    
    ${testSuites.map(suite => `
        <div class="suite">
            <div class="suite-header">${suite.suiteName} (${suite.summary.passed}/${suite.summary.total} passed)</div>
            ${suite.results.map(result => `
                <div class="test-result">
                    <span class="${result.success ? 'success' : 'failure'}">${result.success ? '✓' : '✗'}</span>
                    ${result.testName} (${result.duration}ms)
                    ${result.error ? `<div class="details">Error: ${result.error}</div>` : ''}
                    <div class="details">${JSON.stringify(result.details, null, 2)}</div>
                </div>
            `).join('')}
        </div>
    `).join('')}
</body>
</html>`;
    }
}
