#ifndef WORLD_WRAPPER_H
#define WORLD_WRAPPER_H

#include "world_processor.h"
#include <map>
#include <mutex>
#include <memory>
#include <string>
#include <vector>
#include <once_flag>

/**
 * WORLD Vocoder Wrapper for Node.js Integration
 * Provides high-level interface for voice transformation
 */

namespace WorldWrapper
{

    /**
     * Predefined voice morph profiles for real-time voice transformation
     * Each profile is designed for non-reversible voice morphing
     */
    struct PredefinedProfiles
    {
        static const VoiceMorphProfile SECURE_DEEP_MALE;
        static const VoiceMorphProfile SECURE_HIGH_FEMALE;
        static const VoiceMorphProfile ROBOTIC_SYNTHETIC;
        static const VoiceMorphProfile WHISPER_SOFT;
        static const VoiceMorphProfile DRAMATIC_BASS;
        static const VoiceMorphProfile ETHEREAL_HIGH;
        static const VoiceMorphProfile MECHANICAL_DRONE;
        static const VoiceMorphProfile WARM_TENOR;
        static const VoiceMorphProfile CRYSTAL_SOPRANO;
        static const VoiceMorphProfile DARK_BARITONE;
        static const VoiceMorphProfile BRIGHT_ALTO;
        static const VoiceMorphProfile MYSTERIOUS_ECHO;
        static const VoiceMorphProfile ENERGETIC_YOUNG;
        static const VoiceMorphProfile WISE_ELDER;
        static const VoiceMorphProfile DIGITAL_GLITCH;
        static const VoiceMorphProfile SMOOTH_RADIO;
        static const VoiceMorphProfile INTENSE_GROWL;
        static const VoiceMorphProfile GENTLE_BREEZE;
        static const VoiceMorphProfile POWERFUL_BOOM;
        static const VoiceMorphProfile SUBTLE_SHIFT;

        // Normal profile (only for regular users, not superuser)
        static const VoiceMorphProfile NORMAL_VOICE;
    };

    /**
     * Voice transformation quality levels
     */
    enum class QualityLevel
    {
        REAL_TIME_FAST,     // Optimized for <50ms latency
        REAL_TIME_BALANCED, // Balanced quality/latency <100ms
        HIGH_QUALITY        // Best quality, higher latency
    };

    /**
     * Voice processor manager for handling multiple concurrent sessions
     */
    class VoiceProcessorManager
    {
    public:
        VoiceProcessorManager();
        ~VoiceProcessorManager();

        /**
         * Create a new voice processing session
         * @param sessionId Unique session identifier
         * @param sampleRate Audio sample rate
         * @param frameSize Frame size in samples
         * @param quality Quality level
         * @return Success status
         */
        bool CreateSession(const std::string &sessionId, int sampleRate,
                           int frameSize, QualityLevel quality = QualityLevel::REAL_TIME_BALANCED);

        /**
         * Process audio frame for a specific session
         * @param sessionId Session identifier
         * @param audioData Input audio samples
         * @param audioLength Number of samples
         * @param profileName Profile name or custom profile
         * @return Processed audio samples
         */
        std::vector<float> ProcessAudioFrame(const std::string &sessionId,
                                             const float *audioData, size_t audioLength,
                                             const std::string &profileName);

        /**
         * Process audio frame with custom profile
         * @param sessionId Session identifier
         * @param audioData Input audio samples
         * @param audioLength Number of samples
         * @param customProfile Custom morphing profile
         * @return Processed audio samples
         */
        std::vector<float> ProcessAudioFrameCustom(const std::string &sessionId,
                                                   const float *audioData, size_t audioLength,
                                                   const VoiceMorphProfile &customProfile);

        /**
         * Get predefined profile by name
         * @param profileName Profile name
         * @return Voice morph profile
         */
        static VoiceMorphProfile GetPredefinedProfile(const std::string &profileName);

        /**
         * Get all available profile names
         * @return Vector of profile names
         */
        static std::vector<std::string> GetAvailableProfiles();

        /**
         * Destroy a voice processing session
         * @param sessionId Session identifier
         */
        void DestroySession(const std::string &sessionId);

        /**
         * Get session processing latency
         * @param sessionId Session identifier
         * @return Latency in milliseconds
         */
        double GetSessionLatency(const std::string &sessionId);

        /**
         * Get number of active sessions
         * @return Number of active sessions
         */
        size_t GetActiveSessionCount() const;

    private:
        std::map<std::string, std::unique_ptr<WorldProcessor>> processors_;
        std::mutex processorsMutex_;

        // Performance monitoring
        std::map<std::string, double> sessionLatencies_;

        // Profile cache
        static std::map<std::string, VoiceMorphProfile> profileCache_;
        static std::once_flag profileCacheInitialized_;

        static void InitializeProfileCache();
    };

    /**
     * Utility functions for voice processing
     */
    namespace Utils
    {
        /**
         * Validate audio parameters
         * @param sampleRate Sample rate
         * @param frameSize Frame size
         * @return Validation result
         */
        bool ValidateAudioParameters(int sampleRate, int frameSize);

        /**
         * Calculate optimal frame size for given sample rate
         * @param sampleRate Sample rate
         * @param targetLatencyMs Target latency in milliseconds
         * @return Optimal frame size
         */
        int CalculateOptimalFrameSize(int sampleRate, double targetLatencyMs = 20.0);

        /**
         * Normalize audio samples
         * @param audioData Audio samples to normalize
         * @param targetLevel Target RMS level (0.0-1.0)
         */
        void NormalizeAudio(std::vector<float> &audioData, float targetLevel = 0.7f);

        /**
         * Apply fade in/out to prevent clicks
         * @param audioData Audio samples
         * @param fadeLength Fade length in samples
         */
        void ApplyFadeInOut(std::vector<float> &audioData, int fadeLength = 64);

        /**
         * Check if profile is suitable for user type
         * @param profileName Profile name
         * @param isSuperuser Whether user is superuser
         * @return Whether profile is allowed
         */
        bool IsProfileAllowedForUser(const std::string &profileName, bool isSuperuser);
    }

} // namespace WorldWrapper

#endif // WORLD_WRAPPER_H
