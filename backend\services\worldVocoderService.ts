/**
 * WORLD Vocoder Service for Real-time Voice Transformation
 * Provides high-level interface to WORLD vocoder native addon
 * Designed for <100ms latency voice calls with non-reversible morphing
 */

import * as crypto from 'crypto';
import { EventEmitter } from 'events';
import { voiceSecurityService } from './voiceSecurityService';

// Import the native addon (will be compiled)
let worldVocoder: any = null;
try {
  worldVocoder = require('../build/Release/world_vocoder.node');
} catch (error) {
  const errMsg = error instanceof Error ? error.message : String(error);
  console.warn('WORLD vocoder native addon not available:', errMsg);
}

export interface WorldVoiceProfile {
  pitchScale: number;      // 0.7-1.3 (pitch modification)
  spectralWarp: number;    // -10% to +10% (formant shifting)
  reverbAmount: number;    // 0-50% (spatial distortion)
  eqTilt: number;         // -6dB to +6dB (frequency emphasis)
  temporalJitter: number; // Anti-forensic timing variation
  spectralNoise: number;  // Irreversible spectral masking
  antiForensic: boolean;  // Enable anti-forensic processing
}

export interface WorldProcessingConfig {
  sampleRate: number;     // Audio sample rate (typically 48000)
  frameSize: number;      // Frame size in samples (typically 960 for 20ms)
  realTimeMode: boolean;  // Enable real-time optimizations
  qualityLevel: 'fast' | 'balanced' | 'high';
}

export interface WorldFeatures {
  f0: Float64Array;                    // Fundamental frequency
  spectralEnvelope: Float64Array;      // Spectral envelope (flattened 2D)
  aperiodicity: Float64Array;          // Aperiodicity (flattened 2D)
  spectralFrames: number;              // Number of spectral frames
  spectralBins: number;                // Number of spectral bins
}

export interface ProcessingStats {
  latency: number;        // Processing latency in ms
  frameCount: number;     // Total frames processed
  errorCount: number;     // Number of processing errors
  averageLatency: number; // Average latency over session
}

/**
 * WORLD Vocoder Processing Session
 * Manages a single voice processing session with state
 */
class WorldVocoderSession extends EventEmitter {
  private sessionId: string;
  private processor: any = null;
  private config: WorldProcessingConfig;
  private stats: ProcessingStats;
  private isActive: boolean = false;
  private securitySessionId?: string;

  constructor(sessionId: string, config: WorldProcessingConfig) {
    super();
    this.sessionId = sessionId;
    this.config = config;
    this.stats = {
      latency: 0,
      frameCount: 0,
      errorCount: 0,
      averageLatency: 0
    };
  }

  /**
   * Initialize the processing session
   */
  async initialize(): Promise<boolean> {
    if (!worldVocoder) {
      throw new Error('WORLD vocoder native addon not available');
    }

    try {
      this.processor = worldVocoder.initializeProcessor({
        sampleRate: this.config.sampleRate,
        frameSize: this.config.frameSize,
        realTimeMode: this.config.realTimeMode
      });

      // Initialize security session
      this.securitySessionId = await voiceSecurityService.createSecuritySession(
        this.sessionId, // Using sessionId as userId for now
        {
          antiForensicLevel: 'high',
          temporalJitterRange: 0.05,
          spectralNoiseLevel: 0.15,
          glottalDestruction: true,
          formantScrambling: true,
          cryptographicSigning: true,
          originalWaveformPurge: true
        }
      );

      this.isActive = true;
      this.emit('initialized', this.sessionId);
      return true;
    } catch (error) {
      this.emit('error', error);
      return false;
    }
  }

  /**
   * Process audio frame with voice morphing
   */
  async processFrame(audioData: Float32Array, profile: WorldVoiceProfile): Promise<Float32Array | null> {
    if (!this.isActive || !this.processor) {
      return null;
    }

    try {
      const startTime = Date.now();

      let result = worldVocoder.processAudioFrame(
        this.processor,
        audioData,
        profile
      );

      // Apply security transformations if security session is active
      if (this.securitySessionId) {
        const securityResult = await voiceSecurityService.applySecurityTransformations(
          this.securitySessionId,
          result
        );

        if (securityResult) {
          result = securityResult.securedAudio;

          // Add security processing time to stats
          const totalProcessingTime = Date.now() - startTime;
          this.updateStats(totalProcessingTime);

          this.emit('frameProcessed', {
            sessionId: this.sessionId,
            latency: totalProcessingTime,
            inputSize: audioData.length,
            outputSize: result.length,
            securityMetadata: securityResult.securityMetadata,
            securityProcessingTime: securityResult.processingTime
          });
        } else {
          // Fallback if security processing fails
          const processingTime = Date.now() - startTime;
          this.updateStats(processingTime);

          this.emit('frameProcessed', {
            sessionId: this.sessionId,
            latency: processingTime,
            inputSize: audioData.length,
            outputSize: result.length,
            securityError: 'Security processing failed'
          });
        }
      } else {
        const processingTime = Date.now() - startTime;
        this.updateStats(processingTime);

        this.emit('frameProcessed', {
          sessionId: this.sessionId,
          latency: processingTime,
          inputSize: audioData.length,
          outputSize: result.length
        });
      }

      return result;
    } catch (error) {
      this.stats.errorCount++;
      this.emit('processingError', error);
      return null;
    }
  }

  /**
   * Extract WORLD features from audio
   */
  extractFeatures(audioData: Float32Array): WorldFeatures | null {
    if (!this.isActive || !this.processor) {
      return null;
    }

    try {
      return worldVocoder.extractWorldFeatures(this.processor, audioData);
    } catch (error) {
      this.emit('error', error);
      return null;
    }
  }

  /**
   * Synthesize audio from WORLD features
   */
  synthesizeAudio(features: WorldFeatures): Float32Array | null {
    if (!this.isActive || !this.processor) {
      return null;
    }

    try {
      return worldVocoder.synthesizeAudio(this.processor, features);
    } catch (error) {
      this.emit('error', error);
      return null;
    }
  }

  /**
   * Get processing statistics
   */
  getStats(): ProcessingStats {
    return { ...this.stats };
  }

  /**
   * Get security metrics for this session
   */
  getSecurityMetrics() {
    if (!this.securitySessionId) {
      return null;
    }
    return voiceSecurityService.getSecurityMetrics(this.securitySessionId);
  }

  /**
   * Verify audio security
   */
  async verifyAudioSecurity(audioData: Float32Array, metadata: any) {
    if (!this.securitySessionId) {
      return {
        isSecure: false,
        reversibilityRisk: 1.0,
        forensicResistance: 0,
        integrityVerified: false
      };
    }

    return voiceSecurityService.verifyAudioSecurity(
      this.securitySessionId,
      audioData,
      metadata
    );
  }

  /**
   * Cleanup session
   */
  destroy(): void {
    if (this.processor) {
      try {
        worldVocoder.cleanupProcessor(this.processor);
      } catch (error) {
        console.warn('Error cleaning up WORLD processor:', error);
      }
      this.processor = null;
    }

    // Cleanup security session
    if (this.securitySessionId) {
      voiceSecurityService.destroySecuritySession(this.securitySessionId);
      this.securitySessionId = undefined;
    }

    this.isActive = false;
    this.emit('destroyed', this.sessionId);
  }

  private updateStats(latency: number): void {
    this.stats.frameCount++;
    this.stats.latency = latency;
    this.stats.averageLatency =
      (this.stats.averageLatency * (this.stats.frameCount - 1) + latency) / this.stats.frameCount;
  }
}

/**
 * WORLD Vocoder Service
 * Manages multiple voice processing sessions
 */
export class WorldVocoderService extends EventEmitter {
  private sessions: Map<string, WorldVocoderSession> = new Map();
  private defaultConfig: WorldProcessingConfig = {
    sampleRate: 48000,
    frameSize: 960, // 20ms at 48kHz
    realTimeMode: true,
    qualityLevel: 'balanced'
  };

  constructor() {
    super();
    this.checkNativeAddonAvailability();
  }

  /**
   * Check if native addon is available
   */
  isAvailable(): boolean {
    return worldVocoder !== null;
  }

  /**
   * Create a new voice processing session
   */
  async createSession(userId: string, config?: Partial<WorldProcessingConfig>): Promise<string> {
    const sessionId = crypto.randomUUID();
    const sessionConfig = { ...this.defaultConfig, ...config };

    const session = new WorldVocoderSession(sessionId, sessionConfig);

    // Forward session events
    session.on('error', (error) => this.emit('sessionError', sessionId, error));
    session.on('frameProcessed', (data) => this.emit('frameProcessed', data));
    session.on('destroyed', (id) => this.sessions.delete(id));

    const initialized = await session.initialize();
    if (!initialized) {
      throw new Error('Failed to initialize WORLD vocoder session');
    }

    this.sessions.set(sessionId, session);

    this.emit('sessionCreated', {
      sessionId,
      userId,
      config: sessionConfig
    });

    return sessionId;
  }

  /**
   * Process audio frame for a session
   */
  async processAudioFrame(sessionId: string, audioData: Float32Array,
    profile: WorldVoiceProfile): Promise<Float32Array | null> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }
    return await session.processFrame(audioData, profile);
  }

  /**
   * Get session statistics
   */
  getSessionStats(sessionId: string): ProcessingStats | null {
    const session = this.sessions.get(sessionId);
    return session ? session.getStats() : null;
  }

  /**
   * Destroy a session
   */
  destroySession(sessionId: string): boolean {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.destroy();
      return true;
    }
    return false;
  }

  /**
   * Get all active sessions
   */
  getActiveSessions(): string[] {
    return Array.from(this.sessions.keys());
  }

  /**
   * Get total number of active sessions
   */
  getActiveSessionCount(): number {
    return this.sessions.size;
  }

  /**
   * Cleanup all sessions
   */
  cleanup(): void {
    for (const session of this.sessions.values()) {
      session.destroy();
    }
    this.sessions.clear();
  }

  private checkNativeAddonAvailability(): void {
    if (!worldVocoder) {
      console.warn('⚠️ WORLD vocoder native addon not available. Voice morphing will be disabled.');
      console.warn('To enable WORLD vocoder, run: cd backend && npm run build:world');
    } else {
      console.log('✅ WORLD vocoder native addon loaded successfully');
    }
  }
}

// Predefined voice profiles matching the C++ implementation
export const WORLD_VOICE_PROFILES: Record<string, WorldVoiceProfile> = {
  SECURE_DEEP_MALE: {
    pitchScale: 0.75,
    spectralWarp: -8.0,
    reverbAmount: 20.0,
    eqTilt: -3.0,
    temporalJitter: 0.05,
    spectralNoise: 0.15,
    antiForensic: true
  },
  SECURE_HIGH_FEMALE: {
    pitchScale: 1.25,
    spectralWarp: 6.0,
    reverbAmount: 15.0,
    eqTilt: 2.0,
    temporalJitter: 0.03,
    spectralNoise: 0.12,
    antiForensic: true
  },
  ROBOTIC_SYNTHETIC: {
    pitchScale: 0.9,
    spectralWarp: -15.0,
    reverbAmount: 35.0,
    eqTilt: -6.0,
    temporalJitter: 0.1,
    spectralNoise: 0.25,
    antiForensic: true
  },
  // ... Additional profiles will be added
  NORMAL_VOICE: {
    pitchScale: 1.0,
    spectralWarp: 0.0,
    reverbAmount: 0.0,
    eqTilt: 0.0,
    temporalJitter: 0.0,
    spectralNoise: 0.0,
    antiForensic: false
  }
};

// Export singleton instance
export const worldVocoderService = new WorldVocoderService();
